// Code generated by thriftgo (0.3.18). DO NOT EDIT.

package model

import (
	apache_warning "code.byted.org/kitex/apache_monitor"
	"fmt"
	thrift "github.com/cloudwego/kitex/pkg/protocol/bthrift/apache"
	"strings"
)

type SaveConsolePasswordReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	AccountName  string       `thrift:"AccountName,3,required" frugal:"3,required,string" json:"AccountName"`
	Password     string       `thrift:"Password,4,required" frugal:"4,required,string" json:"Password"`
}

func NewSaveConsolePasswordReq() *SaveConsolePasswordReq {
	return &SaveConsolePasswordReq{}
}

func (p *SaveConsolePasswordReq) InitDefault() {
}

func (p *SaveConsolePasswordReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *SaveConsolePasswordReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *SaveConsolePasswordReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *SaveConsolePasswordReq) GetPassword() (v string) {
	return p.Password
}
func (p *SaveConsolePasswordReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *SaveConsolePasswordReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *SaveConsolePasswordReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *SaveConsolePasswordReq) SetPassword(val string) {
	p.Password = val
}

var fieldIDToName_SaveConsolePasswordReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "AccountName",
	4: "Password",
}

func (p *SaveConsolePasswordReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SaveConsolePasswordReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetAccountName bool = false
	var issetPassword bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetPassword = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetPassword {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_SaveConsolePasswordReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_SaveConsolePasswordReq[fieldId]))
}

func (p *SaveConsolePasswordReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *SaveConsolePasswordReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *SaveConsolePasswordReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *SaveConsolePasswordReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Password = _field
	return nil
}

func (p *SaveConsolePasswordReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SaveConsolePasswordReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("SaveConsolePasswordReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SaveConsolePasswordReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *SaveConsolePasswordReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *SaveConsolePasswordReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *SaveConsolePasswordReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Password", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Password); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *SaveConsolePasswordReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveConsolePasswordReq(%+v)", *p)

}

func (p *SaveConsolePasswordReq) DeepEqual(ano *SaveConsolePasswordReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Password) {
		return false
	}
	return true
}

func (p *SaveConsolePasswordReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *SaveConsolePasswordReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *SaveConsolePasswordReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *SaveConsolePasswordReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Password, src) != 0 {
		return false
	}
	return true
}

type SaveConsolePasswordResp struct {
}

func NewSaveConsolePasswordResp() *SaveConsolePasswordResp {
	return &SaveConsolePasswordResp{}
}

func (p *SaveConsolePasswordResp) InitDefault() {
}

var fieldIDToName_SaveConsolePasswordResp = map[int16]string{}

func (p *SaveConsolePasswordResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SaveConsolePasswordResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *SaveConsolePasswordResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("SaveConsolePasswordResp")

	if err = oprot.WriteStructBegin("SaveConsolePasswordResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *SaveConsolePasswordResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaveConsolePasswordResp(%+v)", *p)

}

func (p *SaveConsolePasswordResp) DeepEqual(ano *SaveConsolePasswordResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type CreateConsoleConnEnvReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	AccountName  string       `thrift:"AccountName,3,required" frugal:"3,required,string" json:"AccountName"`
	Env          string       `thrift:"Env,4,required" frugal:"4,required,string" json:"Env"`
}

func NewCreateConsoleConnEnvReq() *CreateConsoleConnEnvReq {
	return &CreateConsoleConnEnvReq{}
}

func (p *CreateConsoleConnEnvReq) InitDefault() {
}

func (p *CreateConsoleConnEnvReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *CreateConsoleConnEnvReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *CreateConsoleConnEnvReq) GetAccountName() (v string) {
	return p.AccountName
}

func (p *CreateConsoleConnEnvReq) GetEnv() (v string) {
	return p.Env
}
func (p *CreateConsoleConnEnvReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *CreateConsoleConnEnvReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *CreateConsoleConnEnvReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *CreateConsoleConnEnvReq) SetEnv(val string) {
	p.Env = val
}

var fieldIDToName_CreateConsoleConnEnvReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "AccountName",
	4: "Env",
}

func (p *CreateConsoleConnEnvReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateConsoleConnEnvReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetAccountName bool = false
	var issetEnv bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnv = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}

	if !issetEnv {
		fieldId = 4
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_CreateConsoleConnEnvReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_CreateConsoleConnEnvReq[fieldId]))
}

func (p *CreateConsoleConnEnvReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *CreateConsoleConnEnvReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *CreateConsoleConnEnvReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *CreateConsoleConnEnvReq) ReadField4(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Env = _field
	return nil
}

func (p *CreateConsoleConnEnvReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateConsoleConnEnvReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("CreateConsoleConnEnvReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateConsoleConnEnvReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *CreateConsoleConnEnvReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *CreateConsoleConnEnvReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *CreateConsoleConnEnvReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Env", thrift.STRING, 4); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Env); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *CreateConsoleConnEnvReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateConsoleConnEnvReq(%+v)", *p)

}

func (p *CreateConsoleConnEnvReq) DeepEqual(ano *CreateConsoleConnEnvReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Env) {
		return false
	}
	return true
}

func (p *CreateConsoleConnEnvReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *CreateConsoleConnEnvReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *CreateConsoleConnEnvReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *CreateConsoleConnEnvReq) Field4DeepEqual(src string) bool {

	if strings.Compare(p.Env, src) != 0 {
		return false
	}
	return true
}

type CreateConsoleConnEnvResp struct {
}

func NewCreateConsoleConnEnvResp() *CreateConsoleConnEnvResp {
	return &CreateConsoleConnEnvResp{}
}

func (p *CreateConsoleConnEnvResp) InitDefault() {
}

var fieldIDToName_CreateConsoleConnEnvResp = map[int16]string{}

func (p *CreateConsoleConnEnvResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateConsoleConnEnvResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *CreateConsoleConnEnvResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("CreateConsoleConnEnvResp")

	if err = oprot.WriteStructBegin("CreateConsoleConnEnvResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *CreateConsoleConnEnvResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateConsoleConnEnvResp(%+v)", *p)

}

func (p *CreateConsoleConnEnvResp) DeepEqual(ano *CreateConsoleConnEnvResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type DescribeConsoleConnEnvsReq struct {
	InstanceId    string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType  InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	AccountName   *string      `thrift:"AccountName,3,optional" frugal:"3,optional,string" json:"AccountName,omitempty"`
	RecoverWindow *bool        `thrift:"RecoverWindow,4,optional" frugal:"4,optional,bool" json:"RecoverWindow,omitempty"`
}

func NewDescribeConsoleConnEnvsReq() *DescribeConsoleConnEnvsReq {
	return &DescribeConsoleConnEnvsReq{}
}

func (p *DescribeConsoleConnEnvsReq) InitDefault() {
}

func (p *DescribeConsoleConnEnvsReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *DescribeConsoleConnEnvsReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

var DescribeConsoleConnEnvsReq_AccountName_DEFAULT string

func (p *DescribeConsoleConnEnvsReq) GetAccountName() (v string) {
	if !p.IsSetAccountName() {
		return DescribeConsoleConnEnvsReq_AccountName_DEFAULT
	}
	return *p.AccountName
}

var DescribeConsoleConnEnvsReq_RecoverWindow_DEFAULT bool

func (p *DescribeConsoleConnEnvsReq) GetRecoverWindow() (v bool) {
	if !p.IsSetRecoverWindow() {
		return DescribeConsoleConnEnvsReq_RecoverWindow_DEFAULT
	}
	return *p.RecoverWindow
}
func (p *DescribeConsoleConnEnvsReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *DescribeConsoleConnEnvsReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *DescribeConsoleConnEnvsReq) SetAccountName(val *string) {
	p.AccountName = val
}
func (p *DescribeConsoleConnEnvsReq) SetRecoverWindow(val *bool) {
	p.RecoverWindow = val
}

var fieldIDToName_DescribeConsoleConnEnvsReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "AccountName",
	4: "RecoverWindow",
}

func (p *DescribeConsoleConnEnvsReq) IsSetAccountName() bool {
	return p.AccountName != nil
}

func (p *DescribeConsoleConnEnvsReq) IsSetRecoverWindow() bool {
	return p.RecoverWindow != nil
}

func (p *DescribeConsoleConnEnvsReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleConnEnvsReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeConsoleConnEnvsReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeConsoleConnEnvsReq[fieldId]))
}

func (p *DescribeConsoleConnEnvsReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *DescribeConsoleConnEnvsReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *DescribeConsoleConnEnvsReq) ReadField3(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.AccountName = _field
	return nil
}
func (p *DescribeConsoleConnEnvsReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *bool
	if v, err := iprot.ReadBool(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.RecoverWindow = _field
	return nil
}

func (p *DescribeConsoleConnEnvsReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleConnEnvsReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeConsoleConnEnvsReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeConsoleConnEnvsReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeConsoleConnEnvsReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *DescribeConsoleConnEnvsReq) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccountName() {
		if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 3); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.AccountName); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *DescribeConsoleConnEnvsReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecoverWindow() {
		if err = oprot.WriteFieldBegin("RecoverWindow", thrift.BOOL, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteBool(*p.RecoverWindow); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *DescribeConsoleConnEnvsReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeConsoleConnEnvsReq(%+v)", *p)

}

func (p *DescribeConsoleConnEnvsReq) DeepEqual(ano *DescribeConsoleConnEnvsReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field4DeepEqual(ano.RecoverWindow) {
		return false
	}
	return true
}

func (p *DescribeConsoleConnEnvsReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *DescribeConsoleConnEnvsReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *DescribeConsoleConnEnvsReq) Field3DeepEqual(src *string) bool {

	if p.AccountName == src {
		return true
	} else if p.AccountName == nil || src == nil {
		return false
	}
	if strings.Compare(*p.AccountName, *src) != 0 {
		return false
	}
	return true
}
func (p *DescribeConsoleConnEnvsReq) Field4DeepEqual(src *bool) bool {

	if p.RecoverWindow == src {
		return true
	} else if p.RecoverWindow == nil || src == nil {
		return false
	}
	if *p.RecoverWindow != *src {
		return false
	}
	return true
}

type DescribeConsoleConnEnvsResp struct {
	ConnInfos []*ConnInfo `thrift:"ConnInfos,1,required" frugal:"1,required,list<ConnInfo>" json:"ConnInfos"`
}

func NewDescribeConsoleConnEnvsResp() *DescribeConsoleConnEnvsResp {
	return &DescribeConsoleConnEnvsResp{}
}

func (p *DescribeConsoleConnEnvsResp) InitDefault() {
}

func (p *DescribeConsoleConnEnvsResp) GetConnInfos() (v []*ConnInfo) {
	return p.ConnInfos
}
func (p *DescribeConsoleConnEnvsResp) SetConnInfos(val []*ConnInfo) {
	p.ConnInfos = val
}

var fieldIDToName_DescribeConsoleConnEnvsResp = map[int16]string{
	1: "ConnInfos",
}

func (p *DescribeConsoleConnEnvsResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleConnEnvsResp")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetConnInfos bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetConnInfos = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetConnInfos {
		fieldId = 1
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_DescribeConsoleConnEnvsResp[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_DescribeConsoleConnEnvsResp[fieldId]))
}

func (p *DescribeConsoleConnEnvsResp) ReadField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return err
	}
	_field := make([]*ConnInfo, 0, size)
	values := make([]ConnInfo, size)
	for i := 0; i < size; i++ {
		_elem := &values[i]
		_elem.InitDefault()

		if err := _elem.Read(iprot); err != nil {
			return err
		}

		_field = append(_field, _elem)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return err
	}
	p.ConnInfos = _field
	return nil
}

func (p *DescribeConsoleConnEnvsResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("DescribeConsoleConnEnvsResp")

	var fieldId int16
	if err = oprot.WriteStructBegin("DescribeConsoleConnEnvsResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *DescribeConsoleConnEnvsResp) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ConnInfos", thrift.LIST, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ConnInfos)); err != nil {
		return err
	}
	for _, v := range p.ConnInfos {
		if err := v.Write(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteListEnd(); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *DescribeConsoleConnEnvsResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DescribeConsoleConnEnvsResp(%+v)", *p)

}

func (p *DescribeConsoleConnEnvsResp) DeepEqual(ano *DescribeConsoleConnEnvsResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ConnInfos) {
		return false
	}
	return true
}

func (p *DescribeConsoleConnEnvsResp) Field1DeepEqual(src []*ConnInfo) bool {

	if len(p.ConnInfos) != len(src) {
		return false
	}
	for i, v := range p.ConnInfos {
		_src := src[i]
		if !v.DeepEqual(_src) {
			return false
		}
	}
	return true
}

type ConnInfo struct {
	Id          string `thrift:"Id,1,required" frugal:"1,required,string" json:"Id"`
	AccountName string `thrift:"AccountName,2,required" frugal:"2,required,string" json:"AccountName"`
	Env         string `thrift:"Env,3,required" frugal:"3,required,string" json:"Env"`
}

func NewConnInfo() *ConnInfo {
	return &ConnInfo{}
}

func (p *ConnInfo) InitDefault() {
}

func (p *ConnInfo) GetId() (v string) {
	return p.Id
}

func (p *ConnInfo) GetAccountName() (v string) {
	return p.AccountName
}

func (p *ConnInfo) GetEnv() (v string) {
	return p.Env
}
func (p *ConnInfo) SetId(val string) {
	p.Id = val
}
func (p *ConnInfo) SetAccountName(val string) {
	p.AccountName = val
}
func (p *ConnInfo) SetEnv(val string) {
	p.Env = val
}

var fieldIDToName_ConnInfo = map[int16]string{
	1: "Id",
	2: "AccountName",
	3: "Env",
}

func (p *ConnInfo) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ConnInfo")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetId bool = false
	var issetAccountName bool = false
	var issetEnv bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetEnv = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetEnv {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ConnInfo[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ConnInfo[fieldId]))
}

func (p *ConnInfo) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Id = _field
	return nil
}
func (p *ConnInfo) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *ConnInfo) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.Env = _field
	return nil
}

func (p *ConnInfo) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ConnInfo")

	var fieldId int16
	if err = oprot.WriteStructBegin("ConnInfo"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ConnInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Id", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Id); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ConnInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ConnInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("Env", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.Env); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *ConnInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConnInfo(%+v)", *p)

}

func (p *ConnInfo) DeepEqual(ano *ConnInfo) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.Id) {
		return false
	}
	if !p.Field2DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field3DeepEqual(ano.Env) {
		return false
	}
	return true
}

func (p *ConnInfo) Field1DeepEqual(src string) bool {

	if strings.Compare(p.Id, src) != 0 {
		return false
	}
	return true
}
func (p *ConnInfo) Field2DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *ConnInfo) Field3DeepEqual(src string) bool {

	if strings.Compare(p.Env, src) != 0 {
		return false
	}
	return true
}

type UpdateConsoleConnEnvReq struct {
	InstanceId   string       `thrift:"InstanceId,1,required" frugal:"1,required,string" json:"InstanceId"`
	InstanceType InstanceType `thrift:"InstanceType,2,required" frugal:"2,required,InstanceType" json:"InstanceType"`
	AccountName  string       `thrift:"AccountName,3,required" frugal:"3,required,string" json:"AccountName"`
	Env          *string      `thrift:"Env,4,optional" frugal:"4,optional,string" json:"Env,omitempty"`
	Password     *string      `thrift:"Password,5,optional" frugal:"5,optional,string" json:"Password,omitempty"`
}

func NewUpdateConsoleConnEnvReq() *UpdateConsoleConnEnvReq {
	return &UpdateConsoleConnEnvReq{}
}

func (p *UpdateConsoleConnEnvReq) InitDefault() {
}

func (p *UpdateConsoleConnEnvReq) GetInstanceId() (v string) {
	return p.InstanceId
}

func (p *UpdateConsoleConnEnvReq) GetInstanceType() (v InstanceType) {
	return p.InstanceType
}

func (p *UpdateConsoleConnEnvReq) GetAccountName() (v string) {
	return p.AccountName
}

var UpdateConsoleConnEnvReq_Env_DEFAULT string

func (p *UpdateConsoleConnEnvReq) GetEnv() (v string) {
	if !p.IsSetEnv() {
		return UpdateConsoleConnEnvReq_Env_DEFAULT
	}
	return *p.Env
}

var UpdateConsoleConnEnvReq_Password_DEFAULT string

func (p *UpdateConsoleConnEnvReq) GetPassword() (v string) {
	if !p.IsSetPassword() {
		return UpdateConsoleConnEnvReq_Password_DEFAULT
	}
	return *p.Password
}
func (p *UpdateConsoleConnEnvReq) SetInstanceId(val string) {
	p.InstanceId = val
}
func (p *UpdateConsoleConnEnvReq) SetInstanceType(val InstanceType) {
	p.InstanceType = val
}
func (p *UpdateConsoleConnEnvReq) SetAccountName(val string) {
	p.AccountName = val
}
func (p *UpdateConsoleConnEnvReq) SetEnv(val *string) {
	p.Env = val
}
func (p *UpdateConsoleConnEnvReq) SetPassword(val *string) {
	p.Password = val
}

var fieldIDToName_UpdateConsoleConnEnvReq = map[int16]string{
	1: "InstanceId",
	2: "InstanceType",
	3: "AccountName",
	4: "Env",
	5: "Password",
}

func (p *UpdateConsoleConnEnvReq) IsSetEnv() bool {
	return p.Env != nil
}

func (p *UpdateConsoleConnEnvReq) IsSetPassword() bool {
	return p.Password != nil
}

func (p *UpdateConsoleConnEnvReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateConsoleConnEnvReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetInstanceId bool = false
	var issetInstanceType bool = false
	var issetAccountName bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceType = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField3(iprot); err != nil {
					goto ReadFieldError
				}
				issetAccountName = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField4(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField5(iprot); err != nil {
					goto ReadFieldError
				}
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetInstanceId {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceType {
		fieldId = 2
		goto RequiredFieldNotSetError
	}

	if !issetAccountName {
		fieldId = 3
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_UpdateConsoleConnEnvReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_UpdateConsoleConnEnvReq[fieldId]))
}

func (p *UpdateConsoleConnEnvReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}
func (p *UpdateConsoleConnEnvReq) ReadField2(iprot thrift.TProtocol) error {

	var _field InstanceType
	if v, err := iprot.ReadI32(); err != nil {
		return err
	} else {
		_field = InstanceType(v)
	}
	p.InstanceType = _field
	return nil
}
func (p *UpdateConsoleConnEnvReq) ReadField3(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.AccountName = _field
	return nil
}
func (p *UpdateConsoleConnEnvReq) ReadField4(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Env = _field
	return nil
}
func (p *UpdateConsoleConnEnvReq) ReadField5(iprot thrift.TProtocol) error {

	var _field *string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = &v
	}
	p.Password = _field
	return nil
}

func (p *UpdateConsoleConnEnvReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateConsoleConnEnvReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("UpdateConsoleConnEnvReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
		if err = p.writeField3(oprot); err != nil {
			fieldId = 3
			goto WriteFieldError
		}
		if err = p.writeField4(oprot); err != nil {
			fieldId = 4
			goto WriteFieldError
		}
		if err = p.writeField5(oprot); err != nil {
			fieldId = 5
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateConsoleConnEnvReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *UpdateConsoleConnEnvReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceType", thrift.I32, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteI32(int32(p.InstanceType)); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *UpdateConsoleConnEnvReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("AccountName", thrift.STRING, 3); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.AccountName); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 3 end error: ", p), err)
}

func (p *UpdateConsoleConnEnvReq) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetEnv() {
		if err = oprot.WriteFieldBegin("Env", thrift.STRING, 4); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Env); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 4 end error: ", p), err)
}

func (p *UpdateConsoleConnEnvReq) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPassword() {
		if err = oprot.WriteFieldBegin("Password", thrift.STRING, 5); err != nil {
			goto WriteFieldBeginError
		}
		if err := oprot.WriteString(*p.Password); err != nil {
			return err
		}
		if err = oprot.WriteFieldEnd(); err != nil {
			goto WriteFieldEndError
		}
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 5 end error: ", p), err)
}

func (p *UpdateConsoleConnEnvReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateConsoleConnEnvReq(%+v)", *p)

}

func (p *UpdateConsoleConnEnvReq) DeepEqual(ano *UpdateConsoleConnEnvReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.InstanceId) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceType) {
		return false
	}
	if !p.Field3DeepEqual(ano.AccountName) {
		return false
	}
	if !p.Field4DeepEqual(ano.Env) {
		return false
	}
	if !p.Field5DeepEqual(ano.Password) {
		return false
	}
	return true
}

func (p *UpdateConsoleConnEnvReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateConsoleConnEnvReq) Field2DeepEqual(src InstanceType) bool {

	if p.InstanceType != src {
		return false
	}
	return true
}
func (p *UpdateConsoleConnEnvReq) Field3DeepEqual(src string) bool {

	if strings.Compare(p.AccountName, src) != 0 {
		return false
	}
	return true
}
func (p *UpdateConsoleConnEnvReq) Field4DeepEqual(src *string) bool {

	if p.Env == src {
		return true
	} else if p.Env == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Env, *src) != 0 {
		return false
	}
	return true
}
func (p *UpdateConsoleConnEnvReq) Field5DeepEqual(src *string) bool {

	if p.Password == src {
		return true
	} else if p.Password == nil || src == nil {
		return false
	}
	if strings.Compare(*p.Password, *src) != 0 {
		return false
	}
	return true
}

type UpdateConsoleConnEnvResp struct {
}

func NewUpdateConsoleConnEnvResp() *UpdateConsoleConnEnvResp {
	return &UpdateConsoleConnEnvResp{}
}

func (p *UpdateConsoleConnEnvResp) InitDefault() {
}

var fieldIDToName_UpdateConsoleConnEnvResp = map[int16]string{}

func (p *UpdateConsoleConnEnvResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateConsoleConnEnvResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *UpdateConsoleConnEnvResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("UpdateConsoleConnEnvResp")

	if err = oprot.WriteStructBegin("UpdateConsoleConnEnvResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *UpdateConsoleConnEnvResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateConsoleConnEnvResp(%+v)", *p)

}

func (p *UpdateConsoleConnEnvResp) DeepEqual(ano *UpdateConsoleConnEnvResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}

type ForgetConsolePasswordReq struct {
	ID         string `thrift:"ID,1,required" frugal:"1,required,string" json:"ID"`
	InstanceId string `thrift:"InstanceId,2,required" frugal:"2,required,string" json:"InstanceId"`
}

func NewForgetConsolePasswordReq() *ForgetConsolePasswordReq {
	return &ForgetConsolePasswordReq{}
}

func (p *ForgetConsolePasswordReq) InitDefault() {
}

func (p *ForgetConsolePasswordReq) GetID() (v string) {
	return p.ID
}

func (p *ForgetConsolePasswordReq) GetInstanceId() (v string) {
	return p.InstanceId
}
func (p *ForgetConsolePasswordReq) SetID(val string) {
	p.ID = val
}
func (p *ForgetConsolePasswordReq) SetInstanceId(val string) {
	p.InstanceId = val
}

var fieldIDToName_ForgetConsolePasswordReq = map[int16]string{
	1: "ID",
	2: "InstanceId",
}

func (p *ForgetConsolePasswordReq) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ForgetConsolePasswordReq")

	var fieldTypeId thrift.TType
	var fieldId int16
	var issetID bool = false
	var issetInstanceId bool = false

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}

		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField1(iprot); err != nil {
					goto ReadFieldError
				}
				issetID = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err = p.ReadField2(iprot); err != nil {
					goto ReadFieldError
				}
				issetInstanceId = true
			} else if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		default:
			if err = iprot.Skip(fieldTypeId); err != nil {
				goto SkipFieldError
			}
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	if !issetID {
		fieldId = 1
		goto RequiredFieldNotSetError
	}

	if !issetInstanceId {
		fieldId = 2
		goto RequiredFieldNotSetError
	}
	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
ReadFieldError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d '%s' error: ", p, fieldId, fieldIDToName_ForgetConsolePasswordReq[fieldId]), err)
SkipFieldError:
	return thrift.PrependError(fmt.Sprintf("%T field %d skip type %d error: ", p, fieldId, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
RequiredFieldNotSetError:
	return thrift.NewTProtocolExceptionWithType(thrift.INVALID_DATA, fmt.Errorf("required field %s is not set", fieldIDToName_ForgetConsolePasswordReq[fieldId]))
}

func (p *ForgetConsolePasswordReq) ReadField1(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.ID = _field
	return nil
}
func (p *ForgetConsolePasswordReq) ReadField2(iprot thrift.TProtocol) error {

	var _field string
	if v, err := iprot.ReadString(); err != nil {
		return err
	} else {
		_field = v
	}
	p.InstanceId = _field
	return nil
}

func (p *ForgetConsolePasswordReq) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ForgetConsolePasswordReq")

	var fieldId int16
	if err = oprot.WriteStructBegin("ForgetConsolePasswordReq"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
		if err = p.writeField1(oprot); err != nil {
			fieldId = 1
			goto WriteFieldError
		}
		if err = p.writeField2(oprot); err != nil {
			fieldId = 2
			goto WriteFieldError
		}
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldError:
	return thrift.PrependError(fmt.Sprintf("%T write field %d error: ", p, fieldId), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ForgetConsolePasswordReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("ID", thrift.STRING, 1); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.ID); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 1 end error: ", p), err)
}

func (p *ForgetConsolePasswordReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err = oprot.WriteFieldBegin("InstanceId", thrift.STRING, 2); err != nil {
		goto WriteFieldBeginError
	}
	if err := oprot.WriteString(p.InstanceId); err != nil {
		return err
	}
	if err = oprot.WriteFieldEnd(); err != nil {
		goto WriteFieldEndError
	}
	return nil
WriteFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 begin error: ", p), err)
WriteFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T write field 2 end error: ", p), err)
}

func (p *ForgetConsolePasswordReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ForgetConsolePasswordReq(%+v)", *p)

}

func (p *ForgetConsolePasswordReq) DeepEqual(ano *ForgetConsolePasswordReq) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	if !p.Field1DeepEqual(ano.ID) {
		return false
	}
	if !p.Field2DeepEqual(ano.InstanceId) {
		return false
	}
	return true
}

func (p *ForgetConsolePasswordReq) Field1DeepEqual(src string) bool {

	if strings.Compare(p.ID, src) != 0 {
		return false
	}
	return true
}
func (p *ForgetConsolePasswordReq) Field2DeepEqual(src string) bool {

	if strings.Compare(p.InstanceId, src) != 0 {
		return false
	}
	return true
}

type ForgetConsolePasswordResp struct {
}

func NewForgetConsolePasswordResp() *ForgetConsolePasswordResp {
	return &ForgetConsolePasswordResp{}
}

func (p *ForgetConsolePasswordResp) InitDefault() {
}

var fieldIDToName_ForgetConsolePasswordResp = map[int16]string{}

func (p *ForgetConsolePasswordResp) Read(iprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ForgetConsolePasswordResp")

	var fieldTypeId thrift.TType
	var fieldId int16

	if _, err = iprot.ReadStructBegin(); err != nil {
		goto ReadStructBeginError
	}

	for {
		_, fieldTypeId, fieldId, err = iprot.ReadFieldBegin()
		if err != nil {
			goto ReadFieldBeginError
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err = iprot.Skip(fieldTypeId); err != nil {
			goto SkipFieldTypeError
		}
		if err = iprot.ReadFieldEnd(); err != nil {
			goto ReadFieldEndError
		}
	}
	if err = iprot.ReadStructEnd(); err != nil {
		goto ReadStructEndError
	}

	return nil
ReadStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read struct begin error: ", p), err)
ReadFieldBeginError:
	return thrift.PrependError(fmt.Sprintf("%T read field %d begin error: ", p, fieldId), err)
SkipFieldTypeError:
	return thrift.PrependError(fmt.Sprintf("%T skip field type %d error", p, fieldTypeId), err)

ReadFieldEndError:
	return thrift.PrependError(fmt.Sprintf("%T read field end error", p), err)
ReadStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
}

func (p *ForgetConsolePasswordResp) Write(oprot thrift.TProtocol) (err error) {

	apache_warning.WarningApache("ForgetConsolePasswordResp")

	if err = oprot.WriteStructBegin("ForgetConsolePasswordResp"); err != nil {
		goto WriteStructBeginError
	}
	if p != nil {
	}
	if err = oprot.WriteFieldStop(); err != nil {
		goto WriteFieldStopError
	}
	if err = oprot.WriteStructEnd(); err != nil {
		goto WriteStructEndError
	}
	return nil
WriteStructBeginError:
	return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err)
WriteFieldStopError:
	return thrift.PrependError(fmt.Sprintf("%T write field stop error: ", p), err)
WriteStructEndError:
	return thrift.PrependError(fmt.Sprintf("%T write struct end error: ", p), err)
}

func (p *ForgetConsolePasswordResp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ForgetConsolePasswordResp(%+v)", *p)

}

func (p *ForgetConsolePasswordResp) DeepEqual(ano *ForgetConsolePasswordResp) bool {
	if p == ano {
		return true
	} else if p == nil || ano == nil {
		return false
	}
	return true
}
