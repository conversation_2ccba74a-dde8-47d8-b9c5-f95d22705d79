// Code generated by Validator v0.2.5. DO NOT EDIT.

package model

import (
	"bytes"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"
)

// unused protection
var (
	_ = fmt.Formatter(nil)
	_ = (*bytes.Buffer)(nil)
	_ = (*strings.Builder)(nil)
	_ = reflect.Type(nil)
	_ = (*regexp.Regexp)(nil)
	_ = time.Nanosecond
)

func (p *SyncDBInstancesReq) IsValid() error {
	return nil
}
func (p *SyncDBInstancesResp) IsValid() error {
	return nil
}
func (p *SyncDBInstanceReq) IsValid() error {
	return nil
}
func (p *SyncDBInstanceResp) IsValid() error {
	return nil
}
func (p *InstanceManagementDetail) IsValid() error {
	return nil
}
func (p *InstanceManagementConfig) IsValid() error {
	return nil
}
func (p *DescribeInstanceManagementReq) IsValid() error {
	if p.InstanceType == nil {
		return fmt.Errorf("field InstanceType not_nil rule failed")
	}
	if p.InstanceId == nil {
		return fmt.Errorf("field InstanceId not_nil rule failed")
	}
	if p.Source == nil {
		return fmt.Errorf("field Source not_nil rule failed")
	}
	return nil
}
func (p *DescribeInstanceManagementResp) IsValid() error {
	return nil
}
func (p *EnableInstanceManagementReq) IsValid() error {
	return nil
}
func (p *EnableInstanceManagementResp) IsValid() error {
	return nil
}
func (p *UpdateInstanceManagementConfigReq) IsValid() error {
	return nil
}
func (p *UpdateInstanceManagementConfigResp) IsValid() error {
	return nil
}
func (p *DisableInstanceStatus) IsValid() error {
	return nil
}
func (p *DisableInstanceManagementReq) IsValid() error {
	return nil
}
func (p *DisableInstanceManagementResp) IsValid() error {
	return nil
}
func (p *RegisterDBInstanceReq) IsValid() error {
	if p.SecurityConfig != nil {
		if err := p.SecurityConfig.IsValid(); err != nil {
			return fmt.Errorf("field SecurityConfig not valid, %w", err)
		}
	}
	return nil
}
func (p *SecurityConfig) IsValid() error {
	return nil
}
func (p *RegisterDBInstanceResp) IsValid() error {
	return nil
}
func (p *DeleteDBInstanceReq) IsValid() error {
	return nil
}
func (p *DeleteDBInstanceResp) IsValid() error {
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesReq) IsValid() error {
	return nil
}
func (p *DescribeRecentlyUsedDBInstancesResp) IsValid() error {
	return nil
}
func (p *UsedInstanceInfo) IsValid() error {
	if p.InstanceSpec != nil {
		if err := p.InstanceSpec.IsValid(); err != nil {
			return fmt.Errorf("field InstanceSpec not valid, %w", err)
		}
	}
	if p.InstanceInspectionInfo != nil {
		if err := p.InstanceInspectionInfo.IsValid(); err != nil {
			return fmt.Errorf("field InstanceInspectionInfo not valid, %w", err)
		}
	}
	return nil
}
func (p *DeleteRecentlyUsedDBInstancesReq) IsValid() error {
	return nil
}
func (p *DeleteRecentlyUsedDBInstancesResp) IsValid() error {
	return nil
}
func (p *AddRecentlyUsedDBInstanceReq) IsValid() error {
	return nil
}
func (p *AddRecentlyUsedDBInstanceResp) IsValid() error {
	return nil
}
func (p *CheckInstanceExistReq) IsValid() error {
	return nil
}
func (p *CheckInstanceExistResp) IsValid() error {
	return nil
}
