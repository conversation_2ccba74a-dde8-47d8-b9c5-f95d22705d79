package actors

import (
	"fmt"
	"time"

	"code.byted.org/infcs/dbw-mgr/biz/consts"
	"code.byted.org/infcs/dbw-mgr/biz/entity"
	"code.byted.org/infcs/dbw-mgr/biz/utils"
	"code.byted.org/infcs/dbw-mgr/gen/dbw-mgr/2018-01-01/kitex_gen/model"
	libutils "code.byted.org/infcs/ds-lib/common/utils"
	fwctx "code.byted.org/infcs/ds-lib/framework/context"
	"github.com/qjpcpu/fp"

	"code.byted.org/infcs/dbw-mgr/biz/shared"
	"code.byted.org/infcs/ds-lib/common/log"
	"code.byted.org/infcs/ds-lib/framework/actorsystem/types"
)

const SQLTaskID = "SqlTaskID"

type CommandCursor struct {
	CommandSetID string `json:"csid"`
}

func (a *SessionActor) executeCommand(ctx types.Context, msg *shared.ExecuteCommand) {
	if !a.checkConnection(ctx, msg.ConnectionId) {
		return
	}
	cs, err := a.cmdRepo.GetCommandSet(ctx, msg.CommandSetId)
	if err != nil {
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_BadCommand})
		log.Info(ctx, "get command fail %v", err)
		return
	}
	if len(cs.Commands) == 0 {
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_BadCommand})
		log.Info(ctx, "command set %v is empty", msg.CommandSetId)
		return
	}
	cmd := cs.Commands[0]
	log.Info(ctx, "execute command cmd extra is %s", utils.Show(cmd.Extra))
	ret := &shared.CheckSessionSuccess{
		SessionId: a.GetSessionID(ctx),
		Source:    a.state.DataSource,
	}
	fp.StreamOf(a.state.Conn).
		Map(func(c *Connection) *shared.SessionConnectionInfo {
			return &shared.SessionConnectionInfo{
				Id:        c.ID,
				Name:      c.Name,
				CurrentDb: c.CurrentDB,
			}
		}).Filter(func(info *shared.SessionConnectionInfo) bool {
		return info.Id == msg.ConnectionId
	}).ToSlice(&ret.ConnectionList)
	for _, Connection := range ret.ConnectionList {
		a.state.DataSource.Db = Connection.CurrentDb
	}
	a.state.IgnoreSecurityCheck = msg.IgnoreSecurityCheck
	ctx.SaveState()
	err = a.PriSvc.SecurityCheck(ctx, cmd.Content, a.state.DataSource, a.state.IgnoreSecurityCheck, model.SqlExecutionType_SqlQuery)
	if err != nil {
		log.Warn(ctx, "SecurityCheck error:%s", err.Error())
		result := &shared.CommandResult{
			CommandId:    cmd.ID,
			Code:         shared.ErrBadCommand,
			ConnectionId: msg.ConnectionId,
			ErrorMessage: err.Error(),
		}
		ctx.Send(ctx.Self(), result)
		_ = ctx.ClientOf(consts.ConnectionActorKind).Send(ctx, msg.ConnectionId, &shared.ForceRollback{})
	} else {
		// 判断如何提交sql：1.connection 2.task 3.ticket（未实现）
		switch cmd.Extra["SqlExecuteType"] {
		case model.SqlExecuteType_Connection.String():
			a.submitToConnection(ctx, cmd, msg.ConnectionId)
		case model.SqlExecuteType_Task.String():
			taskId := a.submitToOnlineDDLTask(ctx, cmd, msg.ConnectionId)
			cs.Commands[0].Extra[SQLTaskID] = taskId
		default:
			a.submitToConnection(ctx, cmd, msg.ConnectionId)
		}
	}

	/* mark command started */
	now := time.Now().UnixMilli()
	cs.MarkCommandExecuting(cmd.ID, now)
	log.Info(ctx, "cmd extra is %s , command set is %s", utils.Show(cmd.Extra), utils.Show(cs.Commands[0]))
	err = a.cmdRepo.SaveCommandSet(ctx, cs)
	if err != nil {
		log.Warn(ctx, "save command set fail %v", err)
		return
	}
	// 给连接加锁
	a.state.ConnLock[msg.ConnectionId] = &CommandCursor{
		CommandSetID: msg.CommandSetId,
	}
	ctx.Respond(&shared.CommandAccepted{})
	log.Info(ctx, "command %s/%s %s submitted ok", msg.CommandSetId, cmd.ID, cmd.Content)
}

/*
checkConnection
1、检查连接是否busy
2、检查session是否包含这个connection,检查这个连接是否是一个dead的连接
3、检查这个连接是否是一个dead的连接
*/
func (a *SessionActor) checkConnection(ctx types.Context, connID string) bool {
	switch {
	case a.state.connectionBusy(connID):
		log.Info(ctx, "connection %s is busy", connID)
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_ConnectionBusy})
		return false
	case !a.state.containsConn(connID) && !a.state.containsDeadConn(connID):
		log.Info(ctx, "connection %s not exist", connID)
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_ConnectionNotExit})
		return false
	case a.state.containsDeadConn(connID):
		log.Info(ctx, "connection %s broken", connID)
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_InvalidConnection})
		return false
	}
	return true
}

func (a *SessionActor) submitToConnection(ctx types.Context, cmd *entity.Command, connectionId string) {
	cnf := a.cnf.Get(ctx)
	err := ctx.ClientOf(consts.ConnectionActorKind).
		Send(ctx, connectionId, &shared.Command{
			CommandId:          cmd.ID,
			Command:            cmd.Content,
			MaxResultCount:     cnf.MaxCommandResultCount,
			MaxResultBytes:     cnf.MaxCommandResultBytes,
			MaxResultCellBytes: cnf.MaxCommandResultCellBytes,
			TimeoutMs:          cnf.CommandResultTimeout * 1000,
		})
	if err != nil {
		log.Warn(ctx, "submit command to connection fail %v", err)
		ctx.Respond(&shared.CommandRejected{Reason: shared.Rejected_InvalidConnection})
		return
	}
}

func (a *SessionActor) submitToOnlineDDLTask(ctx types.Context, cmd *entity.Command, connectionId string) string {
	var (
		IsCreateShardingTable *bool
		ShardingKeyName       *string
		ShardingKeyType       *string

		KillLongTxn          *bool
		RenameDisallowWindow *string
		RplDelayCheckRule    *string
	)
	log.Info(ctx, "ExecuteCommand cmd extra is %s", cmd.Extra["IsCreateShardingTable"])
	if v, ok := cmd.Extra["IsCreateShardingTable"].(bool); ok {
		IsCreateShardingTable = libutils.BoolRef(v)
	}
	if v, ok := cmd.Extra["ShardingKeyName"].(string); ok {
		ShardingKeyName = libutils.StringRef(v)
	}
	if v, ok := cmd.Extra["ShardingKeyType"].(string); ok {
		ShardingKeyType = libutils.StringRef(v)
	}

	if v, ok := cmd.Extra["KillLongTxn"].(bool); ok {
		KillLongTxn = libutils.BoolRef(v)
	}
	if v, ok := cmd.Extra["RenameDisallowWindow"].(string); ok {
		RenameDisallowWindow = libutils.StringRef(v)
	}
	if v, ok := cmd.Extra["RplDelayCheckRule"].(string); ok {
		RplDelayCheckRule = libutils.StringRef(v)
	}

	req := &model.CreateSqlTaskReq{
		InstanceId:   a.state.DataSource.InstanceId,
		InstanceType: model.DSTypePtr(model.DSType(a.state.DataSource.Type)),
		DBName:       a.state.DataSource.Db,
		ExecSQL:      cmd.Content,
		SqlTaskType:  model.SqlTaskTypePtr(model.SqlTaskType_OnlineDDL),
		SqlTaskInfo: &model.SqlTaskInfo{
			ExecuteNow:            libutils.BoolRef(true),
			ScheduleTime:          libutils.StringRef(time.Now().Format(time.RFC3339)),
			IsCreateShardingTable: IsCreateShardingTable,
			ShardingKeyName:       ShardingKeyName,
			ShardingKeyType:       ShardingKeyType,
			// ghost
			KillLongTxn:          KillLongTxn,
			RenameDisallowWindow: RenameDisallowWindow,
			RplDelayCheckRule:    RplDelayCheckRule,
		},
	}
	log.Info(ctx, "ExecuteCommand CreateOnlineDDLSqlTask req is %s", utils.Show(req))
	task, err := a.sqlTaskSvc.CreateOnlineDDLSqlTask(ctx, req)
	if err != nil || task == nil {
		log.Warn(ctx, "ExecuteCommand CreateOnlineDDLSqlTask fail %v", err)
		ctx.Send(ctx.Self(), &shared.CommandResult{
			CommandId:    cmd.ID,
			Code:         shared.ErrExeCommand,
			ErrorMessage: fmt.Sprintf("create online sql task error:%v,task %v", err, task),
			ConnectionId: connectionId,
		})
		return ""
	}
	dsResp, err := a.sqlTaskSvc.DescribeSqlTasks(ctx, &model.DescribeSqlTasksReq{
		SqlTaskId:    task,
		InstanceType: req.InstanceType,
		SqlTaskType:  req.SqlTaskType,
		PageNumber:   libutils.Int32Ref(1),
		PageSize:     libutils.Int32Ref(1),
	})
	if err != nil {
		log.Warn(ctx, "ExecuteCommand DescribeOnlineDDLSqlTasks fail %v", err)
		ctx.Send(ctx.Self(), &shared.CommandResult{
			CommandId:    cmd.ID,
			Code:         shared.ErrExeCommand,
			ErrorMessage: dsResp[0].Result_,
			ConnectionId: connectionId,
		})
		return ""
	}
	a.state.SqlTaskRunning = true
	a.state.SqlTaskInfo = SqlTaskInfo{
		SqlTaskId:     *task,
		CommandId:     cmd.ID,
		ConnectionId:  connectionId,
		SqlTaskStatus: dsResp[0].SqlTaskStatus,
	}
	ctx.SaveState()
	ctx.Send(ctx.Self(), &shared.PollingSqlTask{})
	return *task
}

func (a *SessionActor) pollingSqlTask(ctx types.Context) {
	if !a.state.SqlTaskRunning {
		return
	}

	// 重置时间，防止超时session自己把自己杀死
	a.state.resetIdlePeriod()
	// 重置时间，防止connection自己把自己杀死
	ctx.ClientOf(consts.ConnectionActorKind).Send(ctx, a.state.SqlTaskInfo.ConnectionId, &shared.Ping{})

	if currentTenant := fwctx.GetTenantID(ctx); currentTenant == "" {
		ctx.WithValue(fwctx.BIZ_CONTEXT_KEY, fwctx.NewBizContext())
		if c := fwctx.GetBizContext(ctx); c != nil {
			c.TenantID = a.state.TenantID
			c.UserID = a.state.UserID
		}
	}
	// 检查上次状态是否已经完成
	// 未完成则继续轮询
	// 获取任务状态
	dsResp, err := a.sqlTaskSvc.DescribeSqlTasks(ctx, &model.DescribeSqlTasksReq{
		SqlTaskId:    &a.state.SqlTaskInfo.SqlTaskId,
		InstanceType: model.DSTypePtr(model.DSType(a.state.DataSource.Type)),
		SqlTaskType:  model.SqlTaskTypePtr(model.SqlTaskType_OnlineDDL),
		PageNumber:   libutils.Int32Ref(1),
		PageSize:     libutils.Int32Ref(1),
	})
	if err != nil {
		// 报错，清空state中SqlTask信息
		a.state.SqlTaskRunning = false
		a.state.SqlTaskInfo = SqlTaskInfo{}
		ctx.SaveState()
		ctx.Send(ctx.Self(), &shared.CommandResult{
			CommandId:    a.state.SqlTaskInfo.CommandId,
			Code:         shared.ErrExeCommand,
			ErrorMessage: err.Error(),
			ConnectionId: a.state.SqlTaskInfo.ConnectionId,
		})
		log.Warn(ctx, "ExecuteCommand DescribeOnlineDDLSqlTasks fail %v", err)
		return
	}
	// 如果运行态则更新状态
	if IsSqlTaskInProgress(a.state.SqlTaskInfo.SqlTaskStatus) {
		// Set ReceiveTimeout, 设置下次轮询时间，更新state中SqlTask信息
		a.state.SqlTaskInfo.SqlTaskStatus = dsResp[0].SqlTaskStatus
		ctx.SaveState()
		ctx.SetReceiveTimeout(time.Second * 10)
		return
	} else {
		// 如果终态则发送消息
		var message *shared.CommandResult
		switch dsResp[0].SqlTaskStatus {
		case model.SqlTaskStatus_Success:
			message = &shared.CommandResult{
				CommandId: a.state.SqlTaskInfo.CommandId,
				Code:      shared.ErrCommandOK,
				Type:      shared.TaskId,
				Payload: []*shared.CommandResultChunk{{
					HasMore: false,
					Header:  []string{"sql_task"},
					Rows: []*shared.CommandResultChunk_Row{{
						Cells: []string{"Query Ok , 0 rows affected"},
					}},
					Offset: 1,
				}},
				ConnectionId: a.state.SqlTaskInfo.ConnectionId,
			}
		default:
			message = &shared.CommandResult{
				CommandId:    a.state.SqlTaskInfo.CommandId,
				Code:         shared.ErrExeCommand,
				ErrorMessage: dsResp[0].Result_,
				ConnectionId: a.state.SqlTaskInfo.ConnectionId,
			}
		}
		// 清空state中SqlTask信息
		a.state.SqlTaskRunning = false
		a.state.SqlTaskInfo = SqlTaskInfo{}
		ctx.SaveState()
		ctx.Send(ctx.Self(), message)
	}
}

func IsSqlTaskInProgress(SqlTaskStatus model.SqlTaskStatus) bool {
	if SqlTaskStatus == model.SqlTaskStatus_Init ||
		SqlTaskStatus == model.SqlTaskStatus_Pending ||
		SqlTaskStatus == model.SqlTaskStatus_Precheck ||
		SqlTaskStatus == model.SqlTaskStatus_Running {
		return true
	}
	return false
}
